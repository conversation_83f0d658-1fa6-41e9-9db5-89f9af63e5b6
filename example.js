/**
 * 熵权TOPSIS使用示例
 */

const EntropyTOPSIS = require('./entropy-topsis');

// 示例1：企业绩效评价
function example1() {
    console.log('=== 示例1：企业绩效评价 ===');
    
    // 决策矩阵：5个企业，4个指标
    // 指标：营业收入(万元)、净利润率(%)、资产负债率(%)、流动比率
    const matrix = [
        [1200, 15.2, 45.3, 2.1],  // 企业A
        [980,  12.8, 52.1, 1.8],  // 企业B
        [1500, 18.5, 38.7, 2.5],  // 企业C
        [850,  10.3, 48.9, 1.9],  // 企业D
        [1350, 16.7, 41.2, 2.3]   // 企业E
    ];
    
    // 指标类型：营业收入(max)、净利润率(max)、资产负债率(min)、流动比率(max)
    const criteriaTypes = ['max', 'max', 'min', 'max'];
    
    // 方案和指标名称
    const alternativeNames = ['企业A', '企业B', '企业C', '企业D', '企业E'];
    const criteriaNames = ['营业收入', '净利润率', '资产负债率', '流动比率'];
    
    // 创建TOPSIS分析实例
    const topsis = new EntropyTOPSIS(matrix, criteriaTypes, alternativeNames, criteriaNames);
    
    // 执行分析
    const results = topsis.analyze();
    
    // 打印结果
    topsis.printResults();
    
    return results;
}

// 示例2：供应商选择
function example2() {
    console.log('\n\n=== 示例2：供应商选择 ===');
    
    // 决策矩阵：4个供应商，5个指标
    // 指标：价格(元)、质量评分、交货时间(天)、服务评分、技术能力评分
    const matrix = [
        [120, 85, 7, 78, 82],   // 供应商1
        [135, 92, 5, 85, 88],   // 供应商2
        [110, 78, 9, 72, 75],   // 供应商3
        [125, 88, 6, 80, 85]    // 供应商4
    ];
    
    // 指标类型：价格(min)、质量(max)、交货时间(min)、服务(max)、技术能力(max)
    const criteriaTypes = ['min', 'max', 'min', 'max', 'max'];
    
    const alternativeNames = ['供应商1', '供应商2', '供应商3', '供应商4'];
    const criteriaNames = ['价格', '质量评分', '交货时间', '服务评分', '技术能力'];
    
    const topsis = new EntropyTOPSIS(matrix, criteriaTypes, alternativeNames, criteriaNames);
    const results = topsis.analyze();
    
    topsis.printResults();
    
    return results;
}

// 示例3：投资项目评价
function example3() {
    console.log('\n\n=== 示例3：投资项目评价 ===');
    
    // 决策矩阵：3个投资项目，4个指标
    // 指标：预期收益率(%)、投资风险评分、投资额(万元)、回收期(年)
    const matrix = [
        [12.5, 6.2, 500, 3.2],  // 项目A
        [15.8, 7.8, 800, 4.1],  // 项目B
        [10.3, 4.5, 300, 2.8]   // 项目C
    ];
    
    // 指标类型：收益率(max)、风险(min)、投资额(min)、回收期(min)
    const criteriaTypes = ['max', 'min', 'min', 'min'];
    
    const alternativeNames = ['项目A', '项目B', '项目C'];
    const criteriaNames = ['预期收益率', '投资风险', '投资额', '回收期'];
    
    const topsis = new EntropyTOPSIS(matrix, criteriaTypes, alternativeNames, criteriaNames);
    const results = topsis.analyze();
    
    topsis.printResults();
    
    return results;
}

// 运行所有示例
function runAllExamples() {
    try {
        const results1 = example1();
        const results2 = example2();
        const results3 = example3();
        
        console.log('\n=== 所有示例运行完成 ===');
        
        return {
            example1: results1,
            example2: results2,
            example3: results3
        };
    } catch (error) {
        console.error('运行示例时出错:', error.message);
    }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
    runAllExamples();
}

module.exports = {
    example1,
    example2,
    example3,
    runAllExamples
};

example1();