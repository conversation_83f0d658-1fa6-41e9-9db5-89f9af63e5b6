/**
 * 熵权TOPSIS多属性决策分析方法
 * Entropy Weight TOPSIS (Technique for Order Preference by Similarity to Ideal Solution)
 */

class EntropyTOPSIS {
    /**
     * 构造函数
     * @param {Array<Array<number>>} matrix - 决策矩阵，行为方案，列为指标
     * @param {Array<string>} criteriaTypes - 指标类型数组，'max'表示效益型，'min'表示成本型
     * @param {Array<string>} alternativeNames - 方案名称数组（可选）
     * @param {Array<string>} criteriaNames - 指标名称数组（可选）
     */
    constructor(matrix, criteriaTypes, alternativeNames = null, criteriaNames = null) {
        this.originalMatrix = matrix.map(row => [...row]); // 深拷贝
        this.criteriaTypes = criteriaTypes;
        this.alternativeNames = alternativeNames || matrix.map((_, i) => `方案${i + 1}`);
        this.criteriaNames = criteriaNames || criteriaTypes.map((_, i) => `指标${i + 1}`);

        // 验证输入数据
        this.validateInput();

        // 计算结果存储
        this.normalizedMatrix = null;
        this.weights = null;
        this.weightedMatrix = null;
        this.idealSolution = null;
        this.negativeIdealSolution = null;
        this.distances = null;
        this.scores = null;
        this.ranking = null;
    }

    /**
     * 验证输入数据
     */
    validateInput() {
        if (!this.originalMatrix || this.originalMatrix.length === 0) {
            throw new Error('决策矩阵不能为空');
        }

        const cols = this.originalMatrix[0].length;
        if (this.originalMatrix.some(row => row.length !== cols)) {
            throw new Error('决策矩阵必须是矩形矩阵');
        }

        if (this.criteriaTypes.length !== cols) {
            throw new Error('指标类型数组长度必须与矩阵列数相等');
        }

        if (this.alternativeNames.length !== this.originalMatrix.length) {
            throw new Error('方案名称数组长度必须与矩阵行数相等');
        }
    }

    /**
     * 数据正向化处理
     * 将成本型指标转换为效益型指标
     */
    positiveOrientation() {
        const matrix = this.originalMatrix.map(row => [...row]);

        for (let j = 0; j < this.criteriaTypes.length; j++) {
            if (this.criteriaTypes[j] === 'min') {
                // 成本型指标：使用倒数法或最大值减去当前值
                const maxVal = Math.max(...matrix.map(row => row[j]));
                for (let i = 0; i < matrix.length; i++) {
                    matrix[i][j] = maxVal - matrix[i][j];
                }
            }
        }

        return matrix;
    }

    /**
     * 数据标准化（归一化）
     * 使用向量归一化方法
     */
    normalize(matrix) {
        const normalizedMatrix = matrix.map(row => [...row]);

        for (let j = 0; j < matrix[0].length; j++) {
            // 计算每列的平方和
            const sumSquares = matrix.reduce((sum, row) => sum + row[j] * row[j], 0);
            const norm = Math.sqrt(sumSquares);

            // 归一化
            for (let i = 0; i < matrix.length; i++) {
                normalizedMatrix[i][j] = matrix[i][j] / norm;
            }
        }

        return normalizedMatrix;
    }

    /**
     * 计算熵权
     */
    calculateEntropyWeights(normalizedMatrix) {
        const m = normalizedMatrix.length; // 方案数
        const n = normalizedMatrix[0].length; // 指标数
        const weights = [];

        for (let j = 0; j < n; j++) {
            // 计算每个指标值的比重
            const columnSum = normalizedMatrix.reduce((sum, row) => sum + row[j], 0);

            // 计算第j个指标的熵值
            let entropy = 0;

            for (let i = 0; i < m; i++) {
                const pij = normalizedMatrix[i][j] / columnSum;
                if (pij > 0) {
                    entropy -= pij * Math.log(pij);
                }
            }

            // 标准化熵值
            entropy = entropy / Math.log(m);

            // 计算差异系数（信息效用值）
            const gj = 1 - entropy;
            weights.push(Math.max(gj, 0)); // 确保权重非负
        }

        // 归一化权重
        const sumWeights = weights.reduce((sum, w) => sum + w, 0);
        if (sumWeights === 0) {
            // 如果所有权重都为0，则平均分配
            return new Array(n).fill(1 / n);
        }
        return weights.map(w => w / sumWeights);
    }

    /**
     * 构建加权标准化决策矩阵
     */
    buildWeightedMatrix(normalizedMatrix, weights) {
        return normalizedMatrix.map(row =>
            row.map((val, j) => val * weights[j])
        );
    }

    /**
     * 确定正理想解和负理想解
     */
    findIdealSolutions(weightedMatrix) {
        const n = weightedMatrix[0].length;
        const idealSolution = [];
        const negativeIdealSolution = [];

        for (let j = 0; j < n; j++) {
            const column = weightedMatrix.map(row => row[j]);
            // 对于正向化后的数据，都是越大越好
            idealSolution.push(Math.max(...column));
            negativeIdealSolution.push(Math.min(...column));
        }

        return { idealSolution, negativeIdealSolution };
    }

    /**
     * 计算欧几里得距离
     */
    calculateDistance(vector1, vector2) {
        return Math.sqrt(
            vector1.reduce((sum, val, i) => sum + Math.pow(val - vector2[i], 2), 0)
        );
    }

    /**
     * 计算各方案到理想解的距离
     */
    calculateDistances(weightedMatrix, idealSolution, negativeIdealSolution) {
        return weightedMatrix.map(row => ({
            toIdeal: this.calculateDistance(row, idealSolution),
            toNegativeIdeal: this.calculateDistance(row, negativeIdealSolution)
        }));
    }

    /**
     * 计算相对贴近度
     */
    calculateScores(distances) {
        return distances.map(d =>
            d.toNegativeIdeal / (d.toIdeal + d.toNegativeIdeal)
        );
    }

    /**
     * 执行完整的熵权TOPSIS分析
     */
    analyze() {
        // 1. 数据正向化
        const positiveMatrix = this.positiveOrientation();

        // 2. 数据标准化
        this.normalizedMatrix = this.normalize(positiveMatrix);

        // 3. 计算熵权
        this.weights = this.calculateEntropyWeights(this.normalizedMatrix);

        // 4. 构建加权标准化决策矩阵
        this.weightedMatrix = this.buildWeightedMatrix(this.normalizedMatrix, this.weights);

        // 5. 确定理想解
        const solutions = this.findIdealSolutions(this.weightedMatrix);
        this.idealSolution = solutions.idealSolution;
        this.negativeIdealSolution = solutions.negativeIdealSolution;

        // 6. 计算距离
        this.distances = this.calculateDistances(
            this.weightedMatrix,
            this.idealSolution,
            this.negativeIdealSolution
        );

        // 7. 计算相对贴近度
        this.scores = this.calculateScores(this.distances);

        // 8. 排序
        this.ranking = this.scores
            .map((score, index) => ({ index, score, name: this.alternativeNames[index] }))
            .sort((a, b) => b.score - a.score);

        return this.getResults();
    }

    /**
     * 获取分析结果
     */
    getResults() {
        return {
            // 基本结果
            weights: this.weights.map((w, i) => ({
                criteria: this.criteriaNames[i],
                weight: w
            })),
            scores: this.scores.map((score, i) => ({
                alternative: this.alternativeNames[i],
                score: score
            })),
            ranking: this.ranking.map((item, rank) => ({
                rank: rank + 1,
                alternative: item.name,
                score: item.score
            })),

            // 详细的中间计算结果
            detailedResults: {
                // 原始决策矩阵 (纯数字矩阵)
                originalMatrix: this.originalMatrix,

                // 标准化矩阵 (纯数字矩阵)
                normalizedMatrix: this.normalizedMatrix,

                // 加权标准化矩阵（权重矩阵）(纯数字矩阵)
                weightedMatrix: this.weightedMatrix,

                // 正理想解 (纯数字向量)
                idealSolution: this.idealSolution,

                // 负理想解 (纯数字向量)
                negativeIdealSolution: this.negativeIdealSolution,

                // 到正负理想解的距离 (纯数字)
                distances: this.distances.map(dist => ({
                    toIdeal: dist.toIdeal,
                    toNegativeIdeal: dist.toNegativeIdeal
                }))
            }
        };
    }


    /**
     * 打印详细结果
     * @param {boolean} showDetailed - 是否显示详细的中间计算结果
     */
    printResults(showDetailed = false) {
        const results = this.getResults();

        console.log('\n=== 熵权TOPSIS分析结果 ===\n');

        console.log('1. 指标权重:');
        results.weights.forEach(w => {
            console.log(`   ${w.criteria}: ${w.weight.toFixed(4)} (${(w.weight * 100).toFixed(2)}%)`);
        });

        console.log('\n2. 各方案得分:');
        results.scores.forEach(s => {
            console.log(`   ${s.alternative}: ${s.score.toFixed(4)}`);
        });

        console.log('\n3. 排序结果:');
        results.ranking.forEach(r => {
            console.log(`   第${r.rank}名: ${r.alternative} (得分: ${r.score.toFixed(4)})`);
        });

        if (showDetailed) {
            this.printDetailedResults(results.detailedResults);
        }
    }

    /**
     * 打印详细的中间计算结果
     */
    printDetailedResults(detailedResults) {
        console.log('\n=== 详细计算过程 ===\n');

        // 1. 原始决策矩阵
        console.log('1. 原始决策矩阵:');
        console.log('   方案\\指标', this.criteriaNames.map(name => name.padEnd(10)).join(''));
        detailedResults.originalMatrix.forEach((row, i) => {
            const values = row.map(val => val.toFixed(2).padEnd(10)).join('');
            console.log(`   ${this.alternativeNames[i].padEnd(8)} ${values}`);
        });

        // 2. 标准化矩阵
        console.log('\n2. 标准化矩阵:');
        console.log('   方案\\指标', this.criteriaNames.map(name => name.padEnd(10)).join(''));
        detailedResults.normalizedMatrix.forEach((row, i) => {
            const values = row.map(val => val.toFixed(4).padEnd(10)).join('');
            console.log(`   ${this.alternativeNames[i].padEnd(8)} ${values}`);
        });

        // 3. 加权标准化矩阵
        console.log('\n3. 加权标准化矩阵:');
        console.log('   方案\\指标', this.criteriaNames.map(name => name.padEnd(10)).join(''));
        detailedResults.weightedMatrix.forEach((row, i) => {
            const values = row.map(val => val.toFixed(4).padEnd(10)).join('');
            console.log(`   ${this.alternativeNames[i].padEnd(8)} ${values}`);
        });

        // 4. 正理想解
        console.log('\n4. 正理想解:');
        detailedResults.idealSolution.forEach((val, j) => {
            console.log(`   ${this.criteriaNames[j]}: ${val.toFixed(4)}`);
        });

        // 5. 负理想解
        console.log('\n5. 负理想解:');
        detailedResults.negativeIdealSolution.forEach((val, j) => {
            console.log(`   ${this.criteriaNames[j]}: ${val.toFixed(4)}`);
        });

        // 6. 距离计算
        console.log('\n6. 到理想解的距离:');
        detailedResults.distances.forEach((dist, i) => {
            console.log(`   ${this.alternativeNames[i]}:`);
            console.log(`     到正理想解距离: ${dist.toIdeal.toFixed(4)}`);
            console.log(`     到负理想解距离: ${dist.toNegativeIdeal.toFixed(4)}`);
            console.log(`     相对贴近度: ${(dist.toNegativeIdeal / (dist.toIdeal + dist.toNegativeIdeal)).toFixed(4)}`);
        });
    }
}

module.exports = EntropyTOPSIS;
