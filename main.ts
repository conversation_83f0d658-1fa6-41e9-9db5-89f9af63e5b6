// 类型定义
type CriteriaType = 'max' | 'min'
type Matrix = number[][]
type Vector = number[]

interface WeightResult {
  criteria: string
  weight: number
}

interface ScoreResult {
  alternative: string
  score: number
}

interface RankingResult {
  rank: number
  alternative: string
  score: number
}

interface RankingItem {
  index: number
  score: number
  name: string
}

interface AnalysisResults {
  weights: WeightResult[]
  scores: ScoreResult[]
  ranking: RankingResult[]
  originalMatrix: Matrix
  normalizedMatrix: Matrix
  weightedMatrix: Matrix
  idealSolution: Vector
  negativeIdealSolution: Vector
  distancesToIdeal: Vector
  distancesToNegativeIdeal: Vector
}

export class EntropyTOPSIS {
  private originalMatrix: Matrix
  private criteriaTypes: CriteriaType[]
  private alternativeNames: string[]
  private criteriaNames: string[]
  private normalizedMatrix: Matrix | null = null
  private weights: Vector | null = null
  private weightedMatrix: Matrix | null = null
  private idealSolution: Vector | null = null
  private negativeIdealSolution: Vector | null = null
  private distancesToIdeal: Vector | null = null
  private distancesToNegativeIdeal: Vector | null = null
  private scores: Vector | null = null
  private ranking: RankingItem[] | null = null

  /**
   * 构造函数
   * @param matrix - 决策矩阵，行为方案，列为指标
   * @param criteriaTypes - 指标类型数组，'max'表示效益型，'min'表示成本型
   * @param alternativeNames - 方案名称数组（可选）
   * @param criteriaNames - 指标名称数组（可选）
   */
  constructor(
    matrix: Matrix,
    criteriaTypes: CriteriaType[],
    alternativeNames?: string[] | null,
    criteriaNames?: string[] | null
  ) {
    this.originalMatrix = matrix.map((row) => [...row]) // 深拷贝
    this.criteriaTypes = criteriaTypes
    this.alternativeNames =
      alternativeNames || matrix.map((_, i) => `方案${i + 1}`)
    this.criteriaNames =
      criteriaNames || criteriaTypes.map((_, i) => `指标${i + 1}`)

    // 验证输入数据
    this.validateInput()
  }

  /**
   * 验证输入数据
   */
  private validateInput(): void {
    if (!this.originalMatrix || this.originalMatrix.length === 0) {
      throw new Error("决策矩阵不能为空");
    }

    const cols = this.originalMatrix[0].length;
    if (this.originalMatrix.some((row) => row.length !== cols)) {
      throw new Error("决策矩阵必须是矩形矩阵");
    }

    if (this.criteriaTypes.length !== cols) {
      throw new Error("指标类型数组长度必须与矩阵列数相等");
    }

    if (this.alternativeNames.length !== this.originalMatrix.length) {
      throw new Error("方案名称数组长度必须与矩阵行数相等");
    }
  }

  /**
   * 数据正向化处理
   * 将成本型指标转换为效益型指标
   */
  private positiveOrientation(): Matrix {
    const matrix = this.originalMatrix.map((row) => [...row]);

    for (let j = 0; j < this.criteriaTypes.length; j++) {
      if (this.criteriaTypes[j] === "min") {
        // 成本型指标：使用倒数法或最大值减去当前值
        const maxVal = Math.max(...matrix.map((row) => row[j]));
        for (let i = 0; i < matrix.length; i++) {
          matrix[i][j] = maxVal - matrix[i][j];
        }
      }
    }

    return matrix;
  }

  /**
   * 数据标准化（归一化）
   * 使用向量归一化方法
   */
  private normalize(matrix: Matrix): Matrix {
    const normalizedMatrix = matrix.map((row) => [...row]);

    for (let j = 0; j < matrix[0].length; j++) {
      // 计算每列的平方和
      const sumSquares = matrix.reduce((sum, row) => sum + row[j] * row[j], 0);
      const norm = Math.sqrt(sumSquares);

      // 归一化
      for (let i = 0; i < matrix.length; i++) {
        normalizedMatrix[i][j] = matrix[i][j] / norm;
      }
    }

    return normalizedMatrix;
  }

  /**
   * 计算熵权
   */
  private calculateEntropyWeights(normalizedMatrix: Matrix): Vector {
    const m = normalizedMatrix.length; // 方案数
    const n = normalizedMatrix[0].length; // 指标数
    const weights: number[] = [];

    for (let j = 0; j < n; j++) {
      // 计算每个指标值的比重
      const columnSum = normalizedMatrix.reduce((sum, row) => sum + row[j], 0);

      // 计算第j个指标的熵值
      let entropy = 0;

      for (let i = 0; i < m; i++) {
        const pij = normalizedMatrix[i][j] / columnSum;
        if (pij > 0) {
          entropy -= pij * Math.log(pij);
        }
      }

      // 标准化熵值
      entropy = entropy / Math.log(m);

      // 计算差异系数（信息效用值）
      const gj = 1 - entropy;
      weights.push(Math.max(gj, 0)); // 确保权重非负
    }

    // 归一化权重
    const sumWeights = weights.reduce((sum, w) => sum + w, 0);
    if (sumWeights === 0) {
      // 如果所有权重都为0，则平均分配
      return new Array(n).fill(1 / n);
    }
    return weights.map((w) => w / sumWeights);
  }

  /**
   * 构建加权标准化决策矩阵
   */
  private buildWeightedMatrix(normalizedMatrix: Matrix, weights: Vector): Matrix {
    return normalizedMatrix.map((row) => row.map((val, j) => val * weights[j]));
  }

  /**
   * 确定正理想解和负理想解
   */
  private findIdealSolutions(weightedMatrix: Matrix): { idealSolution: Vector; negativeIdealSolution: Vector } {
    const n = weightedMatrix[0].length;
    const idealSolution: number[] = [];
    const negativeIdealSolution: number[] = [];

    for (let j = 0; j < n; j++) {
      const column = weightedMatrix.map((row) => row[j]);
      // 对于正向化后的数据，都是越大越好
      idealSolution.push(Math.max(...column));
      negativeIdealSolution.push(Math.min(...column));
    }

    return { idealSolution, negativeIdealSolution };
  }

  /**
   * 计算欧几里得距离
   */
  private calculateDistance(vector1: Vector, vector2: Vector): number {
    return Math.sqrt(
      vector1.reduce((sum, val, i) => sum + Math.pow(val - vector2[i], 2), 0)
    );
  }

  /**
   * 计算各方案到理想解的距离
   */
  private calculateDistances(
    weightedMatrix: Matrix,
    idealSolution: Vector,
    negativeIdealSolution: Vector
  ): { distancesToIdeal: Vector; distancesToNegativeIdeal: Vector } {
    const distancesToIdeal: number[] = [];
    const distancesToNegativeIdeal: number[] = [];

    weightedMatrix.forEach((row) => {
      distancesToIdeal.push(this.calculateDistance(row, idealSolution));
      distancesToNegativeIdeal.push(this.calculateDistance(row, negativeIdealSolution));
    });

    return { distancesToIdeal, distancesToNegativeIdeal };
  }

  /**
   * 计算相对贴近度
   */
  private calculateScores(distancesToIdeal: Vector, distancesToNegativeIdeal: Vector): Vector {
    return distancesToIdeal.map((dIdeal, i) => {
      const dNegative = distancesToNegativeIdeal[i];
      return dNegative / (dIdeal + dNegative);
    });
  }

  /**
   * 执行完整的熵权TOPSIS分析
   */
  public analyze(): AnalysisResults {
    // 1. 数据正向化
    const positiveMatrix = this.positiveOrientation();

    // 2. 数据标准化
    this.normalizedMatrix = this.normalize(positiveMatrix);

    // 3. 计算熵权
    this.weights = this.calculateEntropyWeights(this.normalizedMatrix);

    // 4. 构建加权标准化决策矩阵
    this.weightedMatrix = this.buildWeightedMatrix(
      this.normalizedMatrix,
      this.weights
    );

    // 5. 确定理想解
    const solutions = this.findIdealSolutions(this.weightedMatrix);
    this.idealSolution = solutions.idealSolution;
    this.negativeIdealSolution = solutions.negativeIdealSolution;

    // 6. 计算距离
    const distances = this.calculateDistances(
      this.weightedMatrix,
      this.idealSolution,
      this.negativeIdealSolution
    );
    this.distancesToIdeal = distances.distancesToIdeal;
    this.distancesToNegativeIdeal = distances.distancesToNegativeIdeal;

    // 7. 计算相对贴近度
    this.scores = this.calculateScores(this.distancesToIdeal, this.distancesToNegativeIdeal);

    // 8. 排序
    this.ranking = this.scores
      .map((score, index) => ({
        index,
        score,
        name: this.alternativeNames[index],
      }))
      .sort((a, b) => b.score - a.score);

    return this.getResults();
  }

  /**
   * 获取分析结果
   */
  public getResults(): AnalysisResults {
    if (!this.weights || !this.scores || !this.ranking || !this.normalizedMatrix ||
        !this.weightedMatrix || !this.idealSolution || !this.negativeIdealSolution ||
        !this.distancesToIdeal || !this.distancesToNegativeIdeal) {
      throw new Error('分析尚未完成，请先调用 analyze() 方法');
    }

    return {
      // 基本结果
      weights: this.weights.map((w, i) => ({
        criteria: this.criteriaNames[i],
        weight: w,
      })),
      scores: this.scores.map((score, i) => ({
        alternative: this.alternativeNames[i],
        score: score,
      })),
      ranking: this.ranking.map((item, rank) => ({
        rank: rank + 1,
        alternative: item.name,
        score: item.score,
      })),

      originalMatrix: this.originalMatrix,
      normalizedMatrix: this.normalizedMatrix,
      weightedMatrix: this.weightedMatrix,
      idealSolution: this.idealSolution,
      negativeIdealSolution: this.negativeIdealSolution,
      distancesToIdeal: this.distancesToIdeal,
      distancesToNegativeIdeal: this.distancesToNegativeIdeal,
    };
  }

  /**
   * 打印详细结果
   */
  public printResults(showDetailed: boolean = false): void {
    const results = this.getResults();

    console.log("\n=== 熵权TOPSIS分析结果 ===\n");

    console.log("1. 指标权重:");
    results.weights.forEach((w) => {
      console.log(
        `   ${w.criteria}: ${w.weight.toFixed(4)} (${(w.weight * 100).toFixed(
          2
        )}%)`
      );
    });

    console.log("\n2. 各方案得分:");
    results.scores.forEach((s) => {
      console.log(`   ${s.alternative}: ${s.score.toFixed(4)}`);
    });

    console.log("\n3. 排序结果:");
    results.ranking.forEach((r) => {
      console.log(
        `   第${r.rank}名: ${r.alternative} (得分: ${r.score.toFixed(4)})`
      );
    });

    if (showDetailed) {
      this.printDetailedResults(results);
    }
  }

  /**
   * 打印详细的中间计算结果
   */
  private printDetailedResults(results: AnalysisResults): void {
    console.log("\n=== 详细计算过程 ===\n");

    // 1. 原始决策矩阵
    console.log("1. 原始决策矩阵:");
    console.log(
      "   方案\\指标",
      this.criteriaNames.map((name) => name.padEnd(10)).join("")
    );
    results.originalMatrix.forEach((row, i) => {
      const values = row.map((val) => val.toFixed(2).padEnd(10)).join("");
      console.log(`   ${this.alternativeNames[i].padEnd(8)} ${values}`);
    });

    // 2. 标准化矩阵
    console.log("\n2. 标准化矩阵:");
    console.log(
      "   方案\\指标",
      this.criteriaNames.map((name) => name.padEnd(10)).join("")
    );
    results.normalizedMatrix.forEach((row, i) => {
      const values = row.map((val) => val.toFixed(4).padEnd(10)).join("");
      console.log(`   ${this.alternativeNames[i].padEnd(8)} ${values}`);
    });

    // 3. 加权标准化矩阵
    console.log("\n3. 加权标准化矩阵:");
    console.log(
      "   方案\\指标",
      this.criteriaNames.map((name) => name.padEnd(10)).join("")
    );
    results.weightedMatrix.forEach((row, i) => {
      const values = row.map((val) => val.toFixed(4).padEnd(10)).join("");
      console.log(`   ${this.alternativeNames[i].padEnd(8)} ${values}`);
    });

    // 4. 正理想解
    console.log("\n4. 正理想解:");
    results.idealSolution.forEach((val, j) => {
      console.log(`   ${this.criteriaNames[j]}: ${val.toFixed(4)}`);
    });

    // 5. 负理想解
    console.log("\n5. 负理想解:");
    results.negativeIdealSolution.forEach((val, j) => {
      console.log(`   ${this.criteriaNames[j]}: ${val.toFixed(4)}`);
    });

    // 6. 距离计算
    console.log("\n6. 到理想解的距离:");
    results.distancesToIdeal.forEach((dIdeal, i) => {
      const dNegative = results.distancesToNegativeIdeal[i];
      console.log(`   ${this.alternativeNames[i]}:`);
      console.log(`     到正理想解距离: ${dIdeal.toFixed(4)}`);
      console.log(`     到负理想解距离: ${dNegative.toFixed(4)}`);
      console.log(
        `     相对贴近度: ${(dNegative / (dIdeal + dNegative)).toFixed(4)}`
      );
    });
  }
}

// 简化的函数接口，用于向后兼容
export interface SimpleTopsisResult {
  normalizedMatrix: Matrix
  weights: Vector
  weightedMatrix: Matrix
  idealSolution: Vector
  negativeIdealSolution: Vector
  positiveDistances: Vector
  negativeDistances: Vector
  scores: Vector
  ranking: Array<{
    originalIndex: number
    score: number
    rank: number
  }>
}

/**
 * 熵权TOPSIS分析的简化函数接口
 * @param matrix - 决策矩阵
 * @param types - 指标类型数组
 * @returns 分析结果
 */
export function entropyTopsis(matrix: Matrix, types: CriteriaType[]): SimpleTopsisResult {
  const topsis = new EntropyTOPSIS(matrix, types)
  const results = topsis.analyze()

  return {
    normalizedMatrix: results.normalizedMatrix,
    weights: results.weights.map(w => w.weight),
    weightedMatrix: results.weightedMatrix,
    idealSolution: results.idealSolution,
    negativeIdealSolution: results.negativeIdealSolution,
    positiveDistances: results.distancesToIdeal,
    negativeDistances: results.distancesToNegativeIdeal,
    scores: results.scores.map(s => s.score),
    ranking: results.ranking.map(r => ({
      originalIndex: r.rank - 1, // 转换为0基索引
      score: r.score,
      rank: r.rank
    }))
  }
}
