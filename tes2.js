/**
 * 熵权TOPSIS测试文件
 */

const EntropyTOPSIS = require("./entropy-topsis");

function testBasicFunctionality() {
  console.log("=== 基本功能测试 ===");

  try {
    // 简单的3x3矩阵测试
    const matrix = [
      // 指标: C1(成本), C2(效益), C3(效益), C4(效益)
      [0.92, 5.5, 1, 0.75], // 供应商A
      [0.85, 6, 2.52, 0.54], // 供应商B
      [0.87, 7.6, 4.6, 0.7], // 供应商C
      [0.9, 8.2, 5.42, 0.82], // 供应商D
      [0.7, 7.1, 2.2, 0.6], // 供应商D
      [0.78, 4.2, 1.8, 0.45], // 供应商D
    ];

    const criteriaTypes = ["max", "max", "max", "max"];
    const alternativeNames = ["A", "B", "C", "D", "E", "F"];
    const criteriaNames = ["指标1", "指标2", "指标3", "指标4"];

    const topsis = new EntropyTOPSIS(
      matrix,
      criteriaTypes,
      alternativeNames,
      criteriaNames
    );
    const results = topsis.analyze();

    console.log("✓ 基本功能测试通过");
    console.log(
      "权重总和:",
      results.weights.reduce((sum, w) => sum + w.weight, 0).toFixed(4)
    );
    console.log("最佳方案:", results.ranking[0].alternative);

    // 验证权重都是正数
    const allPositive = results.weights.every((w) => w.weight >= 0);
    if (!allPositive) {
      throw new Error("权重包含负值");
    }

    return true;
  } catch (error) {
    console.error("✗ 基本功能测试失败:", error.message);
    return false;
  }
}

function testInputValidation() {
  console.log("\n=== 输入验证测试 ===");

  let passedTests = 0;
  const totalTests = 4;

  // 测试1：空矩阵
  try {
    new EntropyTOPSIS([], ["max"]);
    console.error("✗ 空矩阵测试失败：应该抛出错误");
  } catch (error) {
    console.log("✓ 空矩阵验证通过");
    passedTests++;
  }

  // 测试2：不规则矩阵
  try {
    new EntropyTOPSIS([[1, 2], [3]], ["max", "min"]);
    console.error("✗ 不规则矩阵测试失败：应该抛出错误");
  } catch (error) {
    console.log("✓ 不规则矩阵验证通过");
    passedTests++;
  }

  // 测试3：指标类型长度不匹配
  try {
    new EntropyTOPSIS(
      [
        [1, 2],
        [3, 4],
      ],
      ["max"]
    );
    console.error("✗ 指标类型长度测试失败：应该抛出错误");
  } catch (error) {
    console.log("✓ 指标类型长度验证通过");
    passedTests++;
  }

  // 测试4：方案名称长度不匹配
  try {
    new EntropyTOPSIS(
      [
        [1, 2],
        [3, 4],
      ],
      ["max", "min"],
      ["A"]
    );
    console.error("✗ 方案名称长度测试失败：应该抛出错误");
  } catch (error) {
    console.log("✓ 方案名称长度验证通过");
    passedTests++;
  }

  console.log(`输入验证测试结果: ${passedTests}/${totalTests} 通过`);
  return passedTests === totalTests;
}

function testMathematicalProperties() {
  console.log("\n=== 数学性质测试 ===");

  const matrix = [
    [100, 80, 60],
    [90, 85, 70],
    [95, 75, 65],
  ];

  const criteriaTypes = ["max", "max", "min"];
  const topsis = new EntropyTOPSIS(matrix, criteriaTypes);
  const results = topsis.analyze();

  let passedTests = 0;
  const totalTests = 3;

  // 测试1：权重和为1
  const weightSum = results.weights.reduce((sum, w) => sum + w.weight, 0);
  if (Math.abs(weightSum - 1) < 1e-10) {
    console.log("✓ 权重和为1验证通过");
    passedTests++;
  } else {
    console.error("✗ 权重和验证失败:", weightSum);
  }

  // 测试2：得分在0-1之间
  const allScoresValid = results.scores.every(
    (s) => s.score >= 0 && s.score <= 1
  );
  if (allScoresValid) {
    console.log("✓ 得分范围验证通过");
    passedTests++;
  } else {
    console.error("✗ 得分范围验证失败");
  }

  // 测试3：排序正确性
  const sortedCorrectly = results.ranking.every((item, index) => {
    if (index === 0) return true;
    return item.score <= results.ranking[index - 1].score;
  });
  if (sortedCorrectly) {
    console.log("✓ 排序正确性验证通过");
    passedTests++;
  } else {
    console.error("✗ 排序正确性验证失败");
  }

  console.log(`数学性质测试结果: ${passedTests}/${totalTests} 通过`);
  return passedTests === totalTests;
}

function runAllTests() {
  console.log("开始运行熵权TOPSIS测试...\n");

 testBasicFunctionality();

}
runAllTests();
