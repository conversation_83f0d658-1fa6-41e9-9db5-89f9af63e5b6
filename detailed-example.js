/**
 * 详细结果输出示例
 * 展示如何获取和显示熵权TOPSIS的详细计算过程
 */

const EntropyTOPSIS = require('./entropy-topsis');

function detailedAnalysisExample() {
    console.log('=== 详细分析示例：学生综合评价 ===');

    // 决策矩阵：5个学生，4个指标
    // 指标：数学成绩、英语成绩、体育成绩、综合素质评分
    const matrix = [
        [85, 78, 92, 88],  // 学生A
        [92, 85, 76, 90],  // 学生B
        [78, 92, 88, 85],  // 学生C
        [88, 80, 85, 92],  // 学生D
        [90, 88, 90, 87]   // 学生E
    ];

    // 所有指标都是效益型（越高越好）
    const criteriaTypes = ['max', 'max', 'max', 'max'];

    const studentNames = ['学生A', '学生B', '学生C', '学生D', '学生E'];
    const criteriaNames = ['数学成绩', '英语成绩', '体育成绩', '综合素质'];

    // 创建TOPSIS分析实例
    const topsis = new EntropyTOPSIS(matrix, criteriaTypes, studentNames, criteriaNames);

    // 执行分析
    const results = topsis.analyze();

    // 显示基本结果
    console.log('\n--- 基本分析结果 ---');
    topsis.printResults(false);

    // 显示详细结果
    console.log('\n--- 详细计算过程 ---');
    topsis.printResults(true);

    return results;
}

function accessDetailedData() {
    console.log('\n\n=== 程序化访问详细数据示例 ===');

    // 简单的3x3示例
    const matrix = [
        [100, 80, 60],
        [90, 85, 70],
        [95, 75, 65]
    ];

    const criteriaTypes = ['max', 'max', 'min'];
    const alternatives = ['方案A', '方案B', '方案C'];
    const criteria = ['收益', '质量', '成本'];

    const topsis = new EntropyTOPSIS(matrix, criteriaTypes, alternatives, criteria);
    const results = topsis.analyze();

    console.log('1. 获取标准化矩阵数据:');
    results.normalizedMatrix.forEach((row, i) => {
        console.log(`   ${alternatives[i]}:`, row.map(v => v.toFixed(4)).join(', '));
    });

    console.log('\n2. 获取权重矩阵数据:');
    results.weightedMatrix.forEach((row, i) => {
        console.log(`   ${alternatives[i]}:`, row.map(v => v.toFixed(4)).join(', '));
    });

    console.log('\n3. 正理想解:');
    results.idealSolution.forEach((val, j) => {
        console.log(`   ${criteria[j]}: ${val.toFixed(4)}`);
    });

    console.log('\n4. 负理想解:');
    results.negativeIdealSolution.forEach((val, j) => {
        console.log(`   ${criteria[j]}: ${val.toFixed(4)}`);
    });

    console.log('\n5. 距离信息:');
    results.distances.forEach((dist, i) => {
        console.log(`   ${alternatives[i]}: D+ = ${dist.toIdeal.toFixed(4)}, D- = ${dist.toNegativeIdeal.toFixed(4)}`);
    });

    return results;
}

function exportToJSON() {
    console.log('\n\n=== 导出JSON格式数据 ===');

    const matrix = [
        [120, 85, 7],
        [135, 92, 5],
        [110, 78, 9]
    ];

    const criteriaTypes = ['min', 'max', 'min'];
    const alternatives = ['供应商1', '供应商2', '供应商3'];
    const criteria = ['价格', '质量', '交货期'];

    const topsis = new EntropyTOPSIS(matrix, criteriaTypes, alternatives, criteria);
    const results = topsis.analyze();

    // 导出为JSON
    const jsonResults = JSON.stringify(results, null, 2);
    console.log('完整结果JSON格式:');
    console.log(jsonResults);

    // 也可以只导出特定部分
    const summaryResults = {
        weights: results.weights,
        ranking: results.ranking,
        normalizedMatrix: results.normalizedMatrix,
        weightedMatrix: results.weightedMatrix,
        idealSolutions: {
            positive: results.idealSolution,
            negative: results.negativeIdealSolution
        },
        distances: results.distances
    };

    console.log('\n简化结果JSON格式:');
    console.log(JSON.stringify(summaryResults, null, 2));

    return results;
}

// 运行所有示例
function runDetailedExamples() {
    try {
        console.log('🔍 熵权TOPSIS详细结果展示\n');

        // 运行详细分析示例
        const example1 = detailedAnalysisExample();

        // 运行程序化访问示例
        const example2 = accessDetailedData();

        // 运行JSON导出示例
        const example3 = exportToJSON();

        console.log('\n✅ 所有详细示例运行完成！');
        console.log('\n💡 使用说明：');
        console.log('1. 调用 printResults(true) 显示详细计算过程');
        console.log('2. 通过 results.detailedResults 访问中间计算结果');
        console.log('3. 可以将结果导出为JSON格式进行进一步处理');

        return { example1, example2, example3 };

    } catch (error) {
        console.error('❌ 运行出错:', error.message);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    runDetailedExamples();
}

module.exports = {
    detailedAnalysisExample,
    accessDetailedData,
    exportToJSON,
    runDetailedExamples
};
