# 熵权TOPSIS多属性决策分析

这是一个用JavaScript实现的熵权TOPSIS（Technique for Order Preference by Similarity to Ideal Solution）多属性决策分析方法。

## 算法简介

TOPSIS是一种多属性决策分析方法，通过计算各方案与理想解的相对贴近度来进行排序。熵权法用于客观确定各指标的权重，避免主观赋权的偏差。

### 算法步骤

1. **数据正向化**：将成本型指标转换为效益型指标
2. **数据标准化**：使用向量归一化方法
3. **计算熵权**：基于信息熵理论确定指标权重
4. **构建加权标准化矩阵**：将权重应用到标准化矩阵
5. **确定理想解**：找出正理想解和负理想解
6. **计算距离**：计算各方案到理想解的欧几里得距离
7. **计算贴近度**：计算相对贴近度并排序

## 使用方法

### 基本用法

```javascript
const EntropyTOPSIS = require('./entropy-topsis');

// 决策矩阵（行为方案，列为指标）
const matrix = [
    [1200, 15.2, 45.3, 2.1],
    [980,  12.8, 52.1, 1.8],
    [1500, 18.5, 38.7, 2.5]
];

// 指标类型（'max'表示效益型，'min'表示成本型）
const criteriaTypes = ['max', 'max', 'min', 'max'];

// 创建分析实例
const topsis = new EntropyTOPSIS(matrix, criteriaTypes);

// 执行分析
const results = topsis.analyze();

// 打印结果
topsis.printResults();
```

### 完整参数用法

```javascript
const alternativeNames = ['方案A', '方案B', '方案C'];
const criteriaNames = ['指标1', '指标2', '指标3', '指标4'];

const topsis = new EntropyTOPSIS(
    matrix, 
    criteriaTypes, 
    alternativeNames, 
    criteriaNames
);
```

## API文档

### 构造函数

```javascript
new EntropyTOPSIS(matrix, criteriaTypes, alternativeNames, criteriaNames)
```

**参数：**
- `matrix` (Array<Array<number>>): 决策矩阵，行为方案，列为指标
- `criteriaTypes` (Array<string>): 指标类型数组，'max'表示效益型，'min'表示成本型
- `alternativeNames` (Array<string>, 可选): 方案名称数组
- `criteriaNames` (Array<string>, 可选): 指标名称数组

### 主要方法

#### `analyze()`
执行完整的熵权TOPSIS分析，返回结果对象。

#### `getResults()`
获取分析结果，包含权重、得分和排序信息。

#### `printResults()`
在控制台打印详细的分析结果。

### 返回结果格式

```javascript
{
    weights: [
        { criteria: '指标名称', weight: 权重值 }
    ],
    scores: [
        { alternative: '方案名称', score: 得分 }
    ],
    ranking: [
        { rank: 排名, alternative: '方案名称', score: 得分 }
    ]
}
```

## 运行示例

```bash
# 运行所有示例
npm start

# 或者
node example.js
```

## 示例场景

项目包含三个实际应用示例：

1. **企业绩效评价**：基于营业收入、净利润率、资产负债率、流动比率等指标
2. **供应商选择**：基于价格、质量、交货时间、服务、技术能力等指标
3. **投资项目评价**：基于预期收益率、投资风险、投资额、回收期等指标

## 注意事项

1. 决策矩阵必须是数值型数据
2. 所有数据必须为正数
3. 指标类型必须正确指定（'max'或'min'）
4. 矩阵必须是完整的矩形矩阵（无缺失值）

## 算法特点

- **客观性**：使用熵权法客观确定权重，减少主观偏差
- **全面性**：同时考虑与正理想解和负理想解的距离
- **适用性**：适用于各种多属性决策问题
- **稳定性**：算法结果稳定可靠

## 许可证

MIT License
