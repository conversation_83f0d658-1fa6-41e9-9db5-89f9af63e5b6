/**
 * 熵权TOPSIS快速开始示例
 * 这个文件展示了如何快速使用熵权TOPSIS进行决策分析
 */

const EntropyTOPSIS = require('./entropy-topsis');

// 快速示例：选择最佳手机
function phoneSelectionExample() {
    console.log('=== 手机选择示例 ===');
    
    // 决策矩阵：4款手机，5个指标
    // 指标：价格(元)、性能评分、电池续航(小时)、拍照评分、屏幕质量评分
    const matrix = [
        [3999, 85, 24, 88, 90],  // iPhone
        [2999, 92, 30, 85, 88],  // 小米
        [4999, 95, 22, 92, 95],  // 华为
        [2499, 78, 28, 80, 82]   // OPPO
    ];
    
    // 指标类型：价格(min)、性能(max)、续航(max)、拍照(max)、屏幕(max)
    const criteriaTypes = ['min', 'max', 'max', 'max', 'max'];
    
    const phoneNames = ['iPhone', '小米', '华为', 'OPPO'];
    const criteriaNames = ['价格', '性能评分', '电池续航', '拍照评分', '屏幕质量'];
    
    // 执行分析
    const topsis = new EntropyTOPSIS(matrix, criteriaTypes, phoneNames, criteriaNames);
    const results = topsis.analyze();
    
    // 显示结果
    console.log('\n指标权重分析:');
    results.weights.forEach(w => {
        console.log(`${w.criteria}: ${(w.weight * 100).toFixed(2)}%`);
    });
    
    console.log('\n推荐排序:');
    results.ranking.forEach((r, index) => {
        const stars = '★'.repeat(5 - index) + '☆'.repeat(index);
        console.log(`${r.rank}. ${r.alternative} ${stars} (综合得分: ${(r.score * 100).toFixed(1)}分)`);
    });
    
    return results;
}

// 自定义数据示例
function customDataExample() {
    console.log('\n\n=== 自定义数据示例 ===');
    console.log('请修改下面的数据来分析您自己的决策问题：\n');
    
    // 您可以修改这里的数据
    const yourMatrix = [
        [100, 80, 60],   // 方案1的各指标值
        [90,  85, 70],   // 方案2的各指标值
        [95,  75, 65]    // 方案3的各指标值
    ];
    
    const yourCriteriaTypes = ['max', 'max', 'min']; // 指标类型
    const yourAlternatives = ['方案A', '方案B', '方案C'];
    const yourCriteria = ['指标1', '指标2', '指标3'];
    
    const topsis = new EntropyTOPSIS(yourMatrix, yourCriteriaTypes, yourAlternatives, yourCriteria);
    const results = topsis.analyze();
    
    topsis.printResults();
    
    return results;
}

// 运行示例
function runQuickStart() {
    console.log('🚀 熵权TOPSIS快速开始\n');
    
    try {
        // 运行手机选择示例
        const phoneResults = phoneSelectionExample();
        
        // 运行自定义数据示例
        const customResults = customDataExample();
        
        console.log('\n✅ 快速开始完成！');
        console.log('\n💡 提示：');
        console.log('1. 修改 quick-start.js 中的数据来分析您自己的问题');
        console.log('2. 查看 example.js 了解更多应用场景');
        console.log('3. 阅读 README.md 了解详细用法');
        
        return { phoneResults, customResults };
        
    } catch (error) {
        console.error('❌ 运行出错:', error.message);
        console.log('\n🔧 请检查：');
        console.log('1. 数据矩阵是否为数值型');
        console.log('2. 指标类型是否正确设置为 "max" 或 "min"');
        console.log('3. 矩阵行列数是否与名称数组长度匹配');
    }
}

// 如果直接运行此文件
if (require.main === module) {
    runQuickStart();
}

module.exports = {
    phoneSelectionExample,
    customDataExample,
    runQuickStart
};
